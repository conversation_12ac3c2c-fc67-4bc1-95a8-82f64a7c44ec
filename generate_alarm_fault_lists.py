import pandas as pd
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def process_excel_files(modbus_file, platform_file, output_dir):
    """
    处理Excel文件并生成报警和故障列表
    
    参数:
        modbus_file: MODBUS点表文件路径
        platform_file: 平台物模型文件路径
        output_dir: 输出目录路径
    """
    try:
        # 读取MODBUS点表文件
        logger.info(f"正在读取MODBUS点表文件: {modbus_file}")
        modbus_df = pd.read_excel(modbus_file)
        
        # 筛选报警、故障和变化标识符
        alarm_condition = modbus_df.iloc[:, 3] == "1~报警；0~正常"  # D列
        fault_condition = modbus_df.iloc[:, 3] == "1~故障；0~正常"  # D列
        change_condition = modbus_df.iloc[:, 3] == "1~合；0~分"  # D列
        
        alarm_keys = ["HMI_" + str(x) for x in modbus_df.loc[alarm_condition].iloc[:, 0]]  # A列
        fault_keys = ["HMI_" + str(x) for x in modbus_df.loc[fault_condition].iloc[:, 0]]  # A列
        change_keys = ["HMI_" + str(x) for x in modbus_df.loc[change_condition].iloc[:, 0]]  # A列
        
        logger.info(f"找到报警标识符: {len(alarm_keys)}个")
        logger.info(f"找到故障标识符: {len(fault_keys)}个")
        logger.info(f"找到变化标识符: {len(change_keys)}个")
        
        # 预处理变化标识符 - 替换HMI_3020_前缀为HMI_30020_
        change_keys = [key.replace('HMI_3020_', 'HMI_30020_') if key.startswith('HMI_3020_') else key 
                      for key in change_keys]
        logger.info(f"预处理后的变化标识符: {len(change_keys)}个")
        
        # 读取平台物模型文件
        logger.info(f"正在读取平台物模型文件: {platform_file}")
        platform_df = pd.read_excel(platform_file)
        
        # 筛选匹配的行
        alarm_matches = platform_df[platform_df['标识符'].isin(alarm_keys)]
        fault_matches = platform_df[platform_df['标识符'].isin(fault_keys)]
        change_matches = platform_df[platform_df['标识符'].isin(change_keys)]
        
        logger.info(f"匹配到报警数据: {len(alarm_matches)}条")
        logger.info(f"匹配到故障数据: {len(fault_matches)}条")
        logger.info(f"匹配到变化数据: {len(change_matches)}条")
        
        # 输出文件
        alarm_output = os.path.join(output_dir, "报警列表.xlsx")
        fault_output = os.path.join(output_dir, "故障列表.xlsx")
        change_output = os.path.join(output_dir, "变化列表.xlsx")
        
        alarm_matches.to_excel(alarm_output, index=False)
        fault_matches.to_excel(fault_output, index=False)
        change_matches.to_excel(change_output, index=False)
        
        logger.info(f"报警列表已保存到: {alarm_output}")
        logger.info(f"故障列表已保存到: {fault_output}")
        logger.info(f"变化列表已保存到: {change_output}")
        
        # 收集未匹配的标识符
        unmatched_alarm_list = list(set(alarm_keys) - set(platform_df['标识符']))
        unmatched_fault_list = list(set(fault_keys) - set(platform_df['标识符']))
        unmatched_change_list = list(set(change_keys) - set(platform_df['标识符']))
        
        # 写入未匹配标识符日志文件
        unmatched_log = os.path.join(output_dir, "未匹配标识符.txt")
        with open(unmatched_log, 'w', encoding='utf-8') as f:
            f.write("未匹配的报警标识符:\n")
            f.write('\n'.join(unmatched_alarm_list) + '\n\n')
            
            f.write("未匹配的故障标识符:\n")
            f.write('\n'.join(unmatched_fault_list) + '\n\n')
            
            f.write("未匹配的变化标识符:\n")
            f.write('\n'.join(unmatched_change_list) + '\n')
        
        logger.info(f"未匹配标识符日志已保存到: {unmatched_log}")
        
        return {
            "alarm_count": len(alarm_matches),
            "fault_count": len(fault_matches),
            "change_count": len(change_matches),
            "unmatched_alarm": len(alarm_keys) - len(alarm_matches),
            "unmatched_fault": len(fault_keys) - len(fault_matches),
            "unmatched_change": len(change_keys) - len(change_matches),
            "unmatched_alarm_list": unmatched_alarm_list,
            "unmatched_fault_list": unmatched_fault_list,
            "unmatched_change_list": unmatched_change_list
        }
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    # 文件路径配置
    modbus_file = os.path.join(os.path.dirname(__file__), 'MODBUS点表0710.xlsx')
    platform_file = os.path.join(os.path.dirname(__file__), 'updated_platform_model.xlsx')
    output_dir = os.path.dirname(__file__)
    
    try:
        stats = process_excel_files(modbus_file, platform_file, output_dir)
        print(f"\n处理完成统计信息:")
        print(f"报警数据匹配成功: {stats['alarm_count']}条")
        print(f"故障数据匹配成功: {stats['fault_count']}条")
        print(f"变化数据匹配成功: {stats['change_count']}条")
        print(f"未匹配的报警标识符: {stats['unmatched_alarm']}个 (已保存到文件)")
        print(f"未匹配的故障标识符: {stats['unmatched_fault']}个 (已保存到文件)")
        print(f"未匹配的变化标识符: {stats['unmatched_change']}个 (已保存到文件)")
    except Exception as e:
        print(f"脚本执行失败: {str(e)}")
        exit(1)