import pandas as pd
import os

def update_model_names(platform_file, modbus_file, output_file):
    """
    根据平台物模型文件中的标志符，在MODBUS点表中查找对应名称并更新物模型名称
    
    参数:
        platform_file: 平台物模型文件路径
        modbus_file: MODBUS点表文件路径
        output_file: 输出文件路径
    """
    try:
        # 读取Excel文件
        platform_df = pd.read_excel(platform_file)
        modbus_df = pd.read_excel(modbus_file)

        # 处理MODBUS数据质量问题
        modbus_df.iloc[:, 0] = modbus_df.iloc[:, 0].fillna('').astype(str)
        
        # 验证列名存在性
        required_columns = {
            'platform': ['标识符', '物模型名称'],
            'modbus': [modbus_df.columns[0], modbus_df.columns[2]]
        }
        for df_type, cols in required_columns.items():
            df = platform_df if df_type == 'platform' else modbus_df
            missing = [col for col in cols if col not in df.columns]
            if missing:
                raise ValueError(f"{df_type}文件缺少必要列: {', '.join(missing)}")

        
        # 初始化统计和日志
        success_count = 0
        manual_review = []
        
        # 遍历平台物模型数据
        for index, row in platform_df.iterrows():
            identifier = row['标识符']
            
            # 规则1: 去掉'HMI_'前缀匹配
            key1 = identifier.replace('HMI_', '')
            match1 = modbus_df[modbus_df.iloc[:, 0] == key1]
            
            if not match1.empty:
                platform_df.at[index, '物模型名称'] = match1.iloc[0, 2]
                success_count += 1
                continue
                
            # 规则2: 去掉'HMI_3'前缀匹配
            key2 = identifier.replace('HMI_3', '')
            match2 = modbus_df[modbus_df.iloc[:, 0] == key2]
            
            if not match2.empty:
                platform_df.at[index, '物模型名称'] = match2.iloc[0, 2]
                success_count += 1
                continue
                
            # 规则3: 去除前导零后后缀匹配
            key3 = key2.lstrip('0')
            if key3:  # 防空字符串处理
                # 查找所有以key3为后缀且满足前缀条件的值
                suffix_matches = modbus_df[modbus_df.iloc[:, 0].str.endswith(key3)]
                # 筛选符合前缀条件的匹配项
                valid_matches = suffix_matches[
                    suffix_matches.iloc[:, 0].str.match(r'^(3[0]*|0[0]*)' + key3 + '$')
                ]
                
                if not valid_matches.empty:
                    platform_df.at[index, '物模型名称'] = valid_matches.iloc[0, 2]
                    success_count += 1
                    continue

            # 规则4: 特殊格式转换
            if '_' in key3:
                try:
                    prefix, suffix = key3.split('_', 1)
                    formatted_suffix = f"{int(suffix):02d}"
                    new_key = f"0{prefix}{formatted_suffix}"
                    
                    match4 = modbus_df[modbus_df.iloc[:, 0] == new_key]
                    
                    if not match4.empty:
                        platform_df.at[index, '物模型名称'] = match4.iloc[0, 2]
                        success_count += 1
                        continue
                except (ValueError, IndexError):
                    pass
                    
            # 规则5: 特殊映射转换
            if '_' in key3:
                try:
                    part1, part2 = key3.split('_', 1)
                    part1_mapping = {
                        '490': 'A1', '491': 'A2', '492': 'A3', '493': 'A4', '494': 'A5', '495': 'A6',
                        '496': 'A7', '497': 'A8', '498': 'A9', '499': 'A10', '500': 'A11', '501': 'A12',
                        '590': 'B1', '591': 'B2', '592': 'B3', '593': 'B4', '594': 'B5', '595': 'B6',
                        '596': 'B7', '597': 'B8', '598': 'B9', '599': 'B10', '600': 'B11', '601': 'B12',
                        '690': 'C1', '691': 'C2', '692': 'C3', '693': 'C4', '694': 'C5', '695': 'C6',
                        '696': 'C7', '697': 'C8', '698': 'C9', '699': 'C10', '700': 'C11', '701': 'C12'
                    }
                    part2_mapping = {
                        '0': '下行光纤断', '1': '封锁', '2': '停止', '3': '启动',
                        '4': '电源故障', '5': '上行光纤断', '6': '下行光纤丢同步', '7': '下行光纤无光',
                        '8': '过压报警', '9': '超温', '10': '欠压', '11': '过压', '12': '过流4',
                        '13': '过流3', '14': '过流2', '15': '过流1'
                    }
                    
                    if part1 in part1_mapping and part2 in part2_mapping:
                        platform_df.at[index, '物模型名称'] = f"{part1_mapping[part1]}{part2_mapping[part2]}"
                        success_count += 1
                        continue
                except (ValueError, IndexError):
                    pass
            
            # 规则6: 固定值映射
            fixed_mappings = {
                'HMI_00001': '控制SVG系统启动',
                'HMI_00002': '控制SVG系统停止',
                'HMI_00003': '控制SVG系统复位',
                'HMI_03600': '控制水冷启动',
                'HMI_03601': '控制水冷停止'
            }
            
            if identifier in fixed_mappings:
                platform_df.at[index, '物模型名称'] = fixed_mappings[identifier]
                success_count += 1
                continue
            
            manual_review.append(identifier)
            print(f"找不到匹配项: {identifier}")
        
        # 保存更新后的文件
        platform_df.to_excel(output_file, index=False)
        
        # 输出统计信息
        print(f"处理完成:\n成功匹配: {success_count}\n需要人工核定: {len(manual_review)}")
        
        # 生成日志文件
        log_file = os.path.join(os.path.dirname(output_file), 'manual_review_log.txt')
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("需要人工核定的标志符:\n")
            f.write('\n'.join(manual_review))
        
    except Exception as e:
        print(f"发生详细错误:\n{str(e)}")
        print(f"平台文件现有列: {platform_df.columns.tolist()}")
        print(f"MODBUS文件现有列: {modbus_df.columns.tolist()}")

if __name__ == "__main__":
    # 文件路径配置
    # platform_file = os.path.join(os.path.dirname(__file__), '1524-平台物模型.xlsx')
    platform_file = os.path.join(os.path.dirname(__file__), 'HMI物模型数据.xlsx')
    modbus_file = os.path.join(os.path.dirname(__file__), 'MODBUS点表0710.xlsx')
    # output_file = os.path.join(os.path.dirname(__file__), 'updated_platform_model.xlsx')
    output_file = os.path.join(os.path.dirname(__file__), 'HMI物模型数据_updated.xlsx')
    
    # 执行更新
    update_model_names(platform_file, modbus_file, output_file)